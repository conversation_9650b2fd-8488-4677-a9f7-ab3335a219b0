import { Injectable, Logger } from '@nestjs/common';
import { ProvidersService } from '../providers/providers.service';
import { SessionsService } from '../sessions/sessions.service';
import { RedisService } from '../redis/redis.service';
import { WebSocketGateway } from '../websocket/websocket.gateway';
import { EventBusService } from './event-bus.service';
import { StateManagerService } from './state-manager.service';
import { RouterEngineService } from './router-engine.service';

export interface UAUIRequest {
  userId: string;
  sessionId: string;
  message: string;
  appType: 'widget' | 'dashboard' | 'crm';
  agentId?: string;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
}

export interface UAUIResponse {
  stream?: boolean;
  chunks?: string[];
  final?: string;
  tool_call?: {
    toolId: string;
    params: Record<string, any>;
  };
  error?: string;
  state_update?: Record<string, any>;
  thinking?: string;
  request_user_input?: {
    requestId: string;
    prompt: string;
    type: 'text' | 'choice' | 'confirmation';
    options?: string[];
  };
}

@Injectable()
export class UAUICore {
  private readonly logger = new Logger(UAUICore.name);

  constructor(
    private readonly providersService: ProvidersService,
    private readonly sessionsService: SessionsService,
    private readonly redisService: RedisService,
    private readonly websocketGateway: WebSocketGateway,
    private readonly eventBus: EventBusService,
    private readonly stateManager: StateManagerService,
    private readonly routerEngine: RouterEngineService,
  ) {}

  async processAIRequest(request: UAUIRequest): Promise<UAUIResponse> {
    try {
      this.logger.log(`Processing UAUI request for user ${request.userId}`);

      // Emit thinking status
      this.websocketGateway.broadcastToUser(request.userId, {
        type: 'thinking_status',
        data: { status: 'processing', message: 'Analyzing your request...' },
        timestamp: new Date().toISOString(),
      });

      // Get or create session
      let session = await this.sessionsService.findOne(request.sessionId);
      if (!session) {
        session = await this.sessionsService.create({
          id: request.sessionId,
          userId: request.userId,
          agentId: request.agentId,
          context: request.context || {},
          memory: {},
        });
      }

      // Load agent memory and context
      const memory = await this.loadAgentMemory(request.agentId, request.userId);
      const context = await this.buildContext(session, request);

      // Prepare AI request
      const aiRequest = {
        model: 'gpt-4', // Default model, should be configurable per agent
        messages: [
          {
            role: 'system' as const,
            content: await this.buildSystemPrompt(request.agentId, context),
          },
          ...memory.conversationHistory || [],
          {
            role: 'user' as const,
            content: request.message,
          },
        ],
        temperature: 0.7,
        maxTokens: 1000,
        userId: request.userId,
        sessionId: request.sessionId,
      };

      // Check if streaming is requested
      if (request.metadata?.stream) {
        return this.handleStreamingRequest(aiRequest, request);
      } else {
        return this.handleRegularRequest(aiRequest, request);
      }
    } catch (error) {
      this.logger.error(`UAUI request failed: ${error.message}`);
      return {
        error: error.message,
      };
    }
  }

  private async handleStreamingRequest(aiRequest: any, request: UAUIRequest): Promise<UAUIResponse> {
    const chunks: string[] = [];
    
    try {
      for await (const chunk of this.providersService.streamResponse(aiRequest)) {
        if (!chunk.isComplete) {
          chunks.push(chunk.content);
          
          // Broadcast chunk to user
          this.websocketGateway.broadcastToUser(request.userId, {
            type: 'text_chunk',
            data: { 
              content: chunk.content,
              sessionId: request.sessionId,
            },
            timestamp: new Date().toISOString(),
          });
        } else {
          // Final chunk - update memory and state
          const fullResponse = chunks.join('');
          await this.updateMemoryAndState(request, fullResponse);
          
          return {
            stream: true,
            chunks,
            final: fullResponse,
          };
        }
      }
    } catch (error) {
      this.logger.error(`Streaming failed: ${error.message}`);
      throw error;
    }

    return { stream: true, chunks };
  }

  private async handleRegularRequest(aiRequest: any, request: UAUIRequest): Promise<UAUIResponse> {
    try {
      const response = await this.providersService.generateResponse(aiRequest);
      
      // Check if response contains tool calls or special instructions
      const parsedResponse = await this.parseResponse(response.content, request);
      
      // Update memory and state
      await this.updateMemoryAndState(request, response.content);
      
      return {
        final: response.content,
        ...parsedResponse,
      };
    } catch (error) {
      this.logger.error(`Regular request failed: ${error.message}`);
      throw error;
    }
  }

  private async parseResponse(content: string, request: UAUIRequest): Promise<Partial<UAUIResponse>> {
    const result: Partial<UAUIResponse> = {};

    // Check for tool calls (simple pattern matching)
    const toolCallMatch = content.match(/\[TOOL_CALL:(\w+)\](.*?)\[\/TOOL_CALL\]/s);
    if (toolCallMatch) {
      try {
        const toolId = toolCallMatch[1];
        const params = JSON.parse(toolCallMatch[2]);
        result.tool_call = { toolId, params };
      } catch (e) {
        this.logger.warn('Failed to parse tool call parameters');
      }
    }

    // Check for user input requests
    const userInputMatch = content.match(/\[REQUEST_INPUT:(.+?)\]/);
    if (userInputMatch) {
      result.request_user_input = {
        requestId: `req_${Date.now()}`,
        prompt: userInputMatch[1],
        type: 'text',
      };
    }

    // Check for state updates
    const stateMatch = content.match(/\[STATE_UPDATE\](.*?)\[\/STATE_UPDATE\]/s);
    if (stateMatch) {
      try {
        result.state_update = JSON.parse(stateMatch[1]);
      } catch (e) {
        this.logger.warn('Failed to parse state update');
      }
    }

    return result;
  }

  private async loadAgentMemory(agentId: string, userId: string): Promise<any> {
    if (!agentId) {
      return { conversationHistory: [] };
    }

    const memory = await this.redisService.getAgentMemory(agentId, userId);
    return memory || { conversationHistory: [] };
  }

  private async buildContext(session: any, request: UAUIRequest): Promise<Record<string, any>> {
    const context = {
      userId: request.userId,
      sessionId: request.sessionId,
      appType: request.appType,
      timestamp: new Date().toISOString(),
      sessionContext: session?.context || {},
      ...request.context,
    };

    // Add state from StateManager
    const appState = await this.stateManager.getState(`${request.userId}:${request.appType}`);
    if (appState) {
      context.appState = appState;
    }

    return context;
  }

  private async buildSystemPrompt(agentId: string, context: Record<string, any>): Promise<string> {
    let systemPrompt = `You are a helpful AI assistant. Current context: ${JSON.stringify(context, null, 2)}`;

    if (agentId) {
      // Load agent-specific prompt from database
      // This would be implemented with actual agent data
      systemPrompt = `You are a specialized AI agent. Use the following context: ${JSON.stringify(context, null, 2)}`;
    }

    return systemPrompt;
  }

  private async updateMemoryAndState(request: UAUIRequest, response: string): Promise<void> {
    try {
      // Update conversation memory
      if (request.agentId) {
        const memory = await this.loadAgentMemory(request.agentId, request.userId);
        memory.conversationHistory = memory.conversationHistory || [];
        
        // Add user message and AI response
        memory.conversationHistory.push(
          { role: 'user', content: request.message },
          { role: 'assistant', content: response }
        );

        // Keep only last 20 messages to prevent memory bloat
        if (memory.conversationHistory.length > 20) {
          memory.conversationHistory = memory.conversationHistory.slice(-20);
        }

        await this.redisService.setAgentMemory(request.agentId, request.userId, memory);
      }

      // Update session
      await this.sessionsService.updateContext(request.sessionId, {
        lastMessage: request.message,
        lastResponse: response,
        lastActivity: new Date().toISOString(),
      });

      // Emit state update event
      this.eventBus.emit('state.updated', {
        userId: request.userId,
        sessionId: request.sessionId,
        agentId: request.agentId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error(`Failed to update memory and state: ${error.message}`);
    }
  }

  async syncState(fromAppId: string, toAppId: string, state: Record<string, any>): Promise<void> {
    await this.stateManager.syncState(fromAppId, toAppId, state);
  }

  onEvent(eventType: string, callback: (payload: any) => void): void {
    this.eventBus.on(eventType, callback);
  }
}
