import { Controller, Post, Body, UseGuards, Request, Get, Param } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { UAUICore, UAUIRequest } from './uaui-core.service';
import { EventBusService } from './event-bus.service';
import { StateManagerService } from './state-manager.service';
import { RouterEngineService } from './router-engine.service';

@ApiTags('UAUI')
@Controller('api/uaui')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth()
export class UAUIController {
  constructor(
    private readonly uauiCore: UAUICore,
    private readonly eventBus: EventBusService,
    private readonly stateManager: StateManagerService,
    private readonly routerEngine: RouterEngineService,
  ) {}

  @Post('process')
  @ApiOperation({ summary: 'Process AI request through UAUI engine' })
  @ApiResponse({ status: 200, description: 'Request processed successfully' })
  async processRequest(@Body() request: UAUIRequest, @Request() req) {
    return this.uauiCore.processAIRequest({
      ...request,
      userId: req.user.id,
    });
  }

  @Post('state/:key')
  @ApiOperation({ summary: 'Set application state' })
  @ApiResponse({ status: 200, description: 'State set successfully' })
  async setState(
    @Param('key') key: string,
    @Body() body: { state: any; ttl?: number },
    @Request() req,
  ) {
    const fullKey = `${req.user.organizationId}:${key}`;
    await this.stateManager.setState(fullKey, body.state, body.ttl);
    return { success: true };
  }

  @Get('state/:key')
  @ApiOperation({ summary: 'Get application state' })
  @ApiResponse({ status: 200, description: 'State retrieved successfully' })
  async getState(@Param('key') key: string, @Request() req) {
    const fullKey = `${req.user.organizationId}:${key}`;
    const state = await this.stateManager.getState(fullKey);
    return { state: state?.state || null };
  }

  @Post('state/:key/update')
  @ApiOperation({ summary: 'Update application state' })
  @ApiResponse({ status: 200, description: 'State updated successfully' })
  async updateState(
    @Param('key') key: string,
    @Body() updates: any,
    @Request() req,
  ) {
    const fullKey = `${req.user.organizationId}:${key}`;
    await this.stateManager.updateState(fullKey, updates);
    return { success: true };
  }

  @Post('state/:key/merge')
  @ApiOperation({ summary: 'Merge application state' })
  @ApiResponse({ status: 200, description: 'State merged successfully' })
  async mergeState(
    @Param('key') key: string,
    @Body() partialState: any,
    @Request() req,
  ) {
    const fullKey = `${req.user.organizationId}:${key}`;
    await this.stateManager.mergeState(fullKey, partialState);
    return { success: true };
  }

  @Post('sync-state')
  @ApiOperation({ summary: 'Sync state between applications' })
  @ApiResponse({ status: 200, description: 'State synced successfully' })
  async syncState(
    @Body() body: { fromKey: string; toKey: string; state: any },
    @Request() req,
  ) {
    const orgPrefix = `${req.user.organizationId}:`;
    await this.uauiCore.syncState(
      orgPrefix + body.fromKey,
      orgPrefix + body.toKey,
      body.state,
    );
    return { success: true };
  }

  @Post('events/emit')
  @ApiOperation({ summary: 'Emit custom event' })
  @ApiResponse({ status: 200, description: 'Event emitted successfully' })
  async emitEvent(
    @Body() body: { eventType: string; data: any },
    @Request() req,
  ) {
    await this.eventBus.emit(body.eventType, {
      ...body.data,
      userId: req.user.id,
      organizationId: req.user.organizationId,
    });
    return { success: true };
  }

  @Get('events/history/:eventType')
  @ApiOperation({ summary: 'Get event history' })
  @ApiResponse({ status: 200, description: 'Event history retrieved' })
  async getEventHistory(
    @Param('eventType') eventType: string,
    @Request() req,
  ) {
    const history = await this.eventBus.getEventHistory(eventType);
    return { history };
  }

  @Post('router/execute')
  @ApiOperation({ summary: 'Execute router command' })
  @ApiResponse({ status: 200, description: 'Router command executed' })
  async executeRouterCommand(
    @Body() command: any,
    @Request() req,
  ) {
    await this.routerEngine.executeCommand({
      ...command,
      userId: req.user.id,
      organizationId: req.user.organizationId,
    });
    return { success: true };
  }

  @Post('router/rules')
  @ApiOperation({ summary: 'Add routing rule' })
  @ApiResponse({ status: 200, description: 'Routing rule added' })
  async addRoutingRule(@Body() rule: any) {
    this.routerEngine.addRoutingRule(rule);
    return { success: true };
  }

  @Get('router/rules')
  @ApiOperation({ summary: 'Get all routing rules' })
  @ApiResponse({ status: 200, description: 'Routing rules retrieved' })
  async getRoutingRules() {
    const rules = this.routerEngine.getAllRoutingRules();
    return { rules };
  }

  @Get('router/stats')
  @ApiOperation({ summary: 'Get router statistics' })
  @ApiResponse({ status: 200, description: 'Router stats retrieved' })
  async getRouterStats() {
    const stats = await this.routerEngine.getRouterStats();
    return { stats };
  }

  @Get('health')
  @ApiOperation({ summary: 'UAUI health check' })
  @ApiResponse({ status: 200, description: 'UAUI system health' })
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      components: {
        uauiCore: 'operational',
        eventBus: 'operational',
        stateManager: 'operational',
        routerEngine: 'operational',
      },
    };
  }
}
