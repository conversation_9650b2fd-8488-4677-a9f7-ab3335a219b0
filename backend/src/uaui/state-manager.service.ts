import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../redis/redis.service';
import { EventBusService } from './event-bus.service';

export interface StateSnapshot {
  id: string;
  state: any;
  timestamp: string;
  version: number;
  userId?: string;
  organizationId?: string;
}

export interface StateDiff {
  path: string;
  oldValue: any;
  newValue: any;
  operation: 'add' | 'update' | 'delete';
}

@Injectable()
export class StateManagerService {
  private readonly logger = new Logger(StateManagerService.name);
  private readonly stateCache = new Map<string, any>();

  constructor(
    private readonly redisService: RedisService,
    private readonly eventBus: EventBusService,
  ) {}

  async setState(key: string, state: any, ttl: number = 1800): Promise<void> {
    try {
      const oldState = await this.getState(key);
      const timestamp = new Date().toISOString();
      const version = await this.getNextVersion(key);

      const stateSnapshot: StateSnapshot = {
        id: key,
        state,
        timestamp,
        version,
      };

      // Store in Redis
      await this.redisService.setState(key, stateSnapshot, ttl);
      
      // Update local cache
      this.stateCache.set(key, stateSnapshot);

      // Calculate and store diff
      if (oldState) {
        const diff = this.calculateDiff(oldState.state, state);
        await this.storeDiff(key, version, diff);
      }

      // Emit state change event
      await this.eventBus.emit('state.changed', {
        key,
        state,
        oldState: oldState?.state,
        timestamp,
        version,
      });

      this.logger.debug(`State updated: ${key} (version ${version})`);
    } catch (error) {
      this.logger.error(`Failed to set state for ${key}: ${error.message}`);
      throw error;
    }
  }

  async getState(key: string): Promise<StateSnapshot | null> {
    try {
      // Check local cache first
      if (this.stateCache.has(key)) {
        return this.stateCache.get(key);
      }

      // Fallback to Redis
      const state = await this.redisService.getState(key);
      if (state) {
        this.stateCache.set(key, state);
      }

      return state;
    } catch (error) {
      this.logger.error(`Failed to get state for ${key}: ${error.message}`);
      return null;
    }
  }

  async deleteState(key: string): Promise<void> {
    try {
      await this.redisService.deleteState(key);
      this.stateCache.delete(key);
      
      await this.eventBus.emit('state.deleted', { key });
      this.logger.debug(`State deleted: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete state for ${key}: ${error.message}`);
      throw error;
    }
  }

  async updateState(key: string, updates: any, ttl: number = 1800): Promise<void> {
    try {
      const currentState = await this.getState(key);
      const newState = currentState ? { ...currentState.state, ...updates } : updates;
      
      await this.setState(key, newState, ttl);
    } catch (error) {
      this.logger.error(`Failed to update state for ${key}: ${error.message}`);
      throw error;
    }
  }

  async mergeState(key: string, partialState: any, ttl: number = 1800): Promise<void> {
    try {
      const currentState = await this.getState(key);
      const newState = this.deepMerge(currentState?.state || {}, partialState);
      
      await this.setState(key, newState, ttl);
    } catch (error) {
      this.logger.error(`Failed to merge state for ${key}: ${error.message}`);
      throw error;
    }
  }

  async syncState(fromKey: string, toKey: string, state: any): Promise<void> {
    try {
      // Get current state from source
      const sourceState = await this.getState(fromKey);
      if (!sourceState) {
        this.logger.warn(`Source state not found for sync: ${fromKey}`);
        return;
      }

      // Merge with provided state
      const mergedState = this.deepMerge(sourceState.state, state);
      
      // Set to target
      await this.setState(toKey, mergedState);
      
      await this.eventBus.emit('state.synced', {
        fromKey,
        toKey,
        state: mergedState,
      });

      this.logger.debug(`State synced from ${fromKey} to ${toKey}`);
    } catch (error) {
      this.logger.error(`Failed to sync state from ${fromKey} to ${toKey}: ${error.message}`);
      throw error;
    }
  }

  async getStateHistory(key: string, limit: number = 10): Promise<StateSnapshot[]> {
    try {
      const historyKey = `state_history:${key}`;
      const history = await this.redisService.get(historyKey);
      return history ? history.slice(-limit) : [];
    } catch (error) {
      this.logger.error(`Failed to get state history for ${key}: ${error.message}`);
      return [];
    }
  }

  async revertState(key: string, version: number): Promise<void> {
    try {
      const history = await this.getStateHistory(key, 100);
      const targetSnapshot = history.find(s => s.version === version);
      
      if (!targetSnapshot) {
        throw new Error(`State version ${version} not found for ${key}`);
      }

      await this.setState(key, targetSnapshot.state);
      
      await this.eventBus.emit('state.reverted', {
        key,
        version,
        state: targetSnapshot.state,
      });

      this.logger.log(`State reverted: ${key} to version ${version}`);
    } catch (error) {
      this.logger.error(`Failed to revert state for ${key}: ${error.message}`);
      throw error;
    }
  }

  async watchState(key: string, callback: (state: any) => void): Promise<void> {
    await this.eventBus.subscribe('state.changed', (event) => {
      if (event.data.key === key) {
        callback(event.data.state);
      }
    });
  }

  async bulkSetState(states: Record<string, any>, ttl: number = 1800): Promise<void> {
    const promises = Object.entries(states).map(([key, state]) =>
      this.setState(key, state, ttl)
    );
    
    await Promise.all(promises);
  }

  async bulkGetState(keys: string[]): Promise<Record<string, any>> {
    const promises = keys.map(async (key) => {
      const state = await this.getState(key);
      return [key, state?.state];
    });
    
    const results = await Promise.all(promises);
    return Object.fromEntries(results);
  }

  private async getNextVersion(key: string): Promise<number> {
    try {
      const versionKey = `state_version:${key}`;
      const currentVersion = await this.redisService.get(versionKey) || 0;
      const nextVersion = currentVersion + 1;
      
      await this.redisService.set(versionKey, nextVersion, 24 * 60 * 60); // 24 hours TTL
      return nextVersion;
    } catch (error) {
      this.logger.error(`Failed to get next version for ${key}: ${error.message}`);
      return 1;
    }
  }

  private calculateDiff(oldState: any, newState: any): StateDiff[] {
    const diffs: StateDiff[] = [];
    
    // Simple diff calculation (can be enhanced with more sophisticated algorithms)
    const allKeys = new Set([...Object.keys(oldState || {}), ...Object.keys(newState || {})]);
    
    for (const key of allKeys) {
      const oldValue = oldState?.[key];
      const newValue = newState?.[key];
      
      if (oldValue === undefined && newValue !== undefined) {
        diffs.push({ path: key, oldValue, newValue, operation: 'add' });
      } else if (oldValue !== undefined && newValue === undefined) {
        diffs.push({ path: key, oldValue, newValue, operation: 'delete' });
      } else if (oldValue !== newValue) {
        diffs.push({ path: key, oldValue, newValue, operation: 'update' });
      }
    }
    
    return diffs;
  }

  private async storeDiff(key: string, version: number, diff: StateDiff[]): Promise<void> {
    try {
      const diffKey = `state_diff:${key}:${version}`;
      await this.redisService.set(diffKey, diff, 24 * 60 * 60); // 24 hours TTL
    } catch (error) {
      this.logger.error(`Failed to store diff for ${key}: ${error.message}`);
    }
  }

  private deepMerge(target: any, source: any): any {
    if (typeof target !== 'object' || target === null) {
      return source;
    }
    
    if (typeof source !== 'object' || source === null) {
      return source;
    }
    
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(target[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  // Cleanup old states
  async cleanupOldStates(olderThanHours: number = 24): Promise<void> {
    try {
      const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
      // Implementation would depend on Redis key patterns and cleanup strategy
      this.logger.log(`Cleaned up states older than ${olderThanHours} hours`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old states: ${error.message}`);
    }
  }
}
