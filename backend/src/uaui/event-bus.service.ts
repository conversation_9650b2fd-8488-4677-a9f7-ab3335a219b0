import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter } from 'events';
import { RedisService } from '../redis/redis.service';

export interface EventPayload {
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
  organizationId?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class EventBusService extends EventEmitter {
  private readonly logger = new Logger(EventBusService.name);

  constructor(private readonly redisService: RedisService) {
    super();
    this.setMaxListeners(100); // Increase max listeners for high-traffic scenarios
  }

  async emit(eventType: string, payload: any): Promise<boolean> {
    try {
      const eventPayload: EventPayload = {
        type: eventType,
        data: payload,
        timestamp: new Date().toISOString(),
        ...payload,
      };

      // Emit locally
      super.emit(eventType, eventPayload);

      // Publish to Redis for cross-instance communication
      await this.redisService.publish(`events:${eventType}`, eventPayload);

      this.logger.debug(`Event emitted: ${eventType}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to emit event ${eventType}: ${error.message}`);
      return false;
    }
  }

  async subscribe(eventType: string, callback: (payload: EventPayload) => void): Promise<void> {
    // Local subscription
    this.on(eventType, callback);

    // Redis subscription for cross-instance events
    await this.redisService.subscribe(`events:${eventType}`, (message) => {
      try {
        callback(message);
      } catch (error) {
        this.logger.error(`Event handler error for ${eventType}: ${error.message}`);
      }
    });

    this.logger.log(`Subscribed to event: ${eventType}`);
  }

  async emitUserEvent(userId: string, eventType: string, data: any): Promise<void> {
    await this.emit(`user.${eventType}`, {
      userId,
      data,
    });
  }

  async emitOrganizationEvent(organizationId: string, eventType: string, data: any): Promise<void> {
    await this.emit(`org.${eventType}`, {
      organizationId,
      data,
    });
  }

  async emitAgentEvent(agentId: string, eventType: string, data: any): Promise<void> {
    await this.emit(`agent.${eventType}`, {
      agentId,
      data,
    });
  }

  async emitToolEvent(toolId: string, eventType: string, data: any): Promise<void> {
    await this.emit(`tool.${eventType}`, {
      toolId,
      data,
    });
  }

  async emitWorkflowEvent(workflowId: string, eventType: string, data: any): Promise<void> {
    await this.emit(`workflow.${eventType}`, {
      workflowId,
      data,
    });
  }

  // Predefined event types for better type safety
  async emitStateUpdate(userId: string, state: any): Promise<void> {
    await this.emitUserEvent(userId, 'state.updated', { state });
  }

  async emitAgentResponse(userId: string, agentId: string, response: string): Promise<void> {
    await this.emitUserEvent(userId, 'agent.response', { agentId, response });
  }

  async emitToolCall(userId: string, toolId: string, params: any): Promise<void> {
    await this.emitUserEvent(userId, 'tool.call', { toolId, params });
  }

  async emitToolResult(userId: string, toolId: string, result: any): Promise<void> {
    await this.emitUserEvent(userId, 'tool.result', { toolId, result });
  }

  async emitError(userId: string, error: string, context?: any): Promise<void> {
    await this.emitUserEvent(userId, 'error', { error, context });
  }

  async emitThinking(userId: string, status: string, message?: string): Promise<void> {
    await this.emitUserEvent(userId, 'thinking', { status, message });
  }

  async emitRequestUserInput(userId: string, requestId: string, prompt: string, type: string = 'text'): Promise<void> {
    await this.emitUserEvent(userId, 'request_user_input', { requestId, prompt, type });
  }

  // Event middleware support
  private middlewares: Array<(event: EventPayload) => Promise<EventPayload | null>> = [];

  addMiddleware(middleware: (event: EventPayload) => Promise<EventPayload | null>): void {
    this.middlewares.push(middleware);
  }

  private async processMiddlewares(event: EventPayload): Promise<EventPayload | null> {
    let processedEvent = event;

    for (const middleware of this.middlewares) {
      try {
        const result = await middleware(processedEvent);
        if (result === null) {
          // Middleware cancelled the event
          return null;
        }
        processedEvent = result;
      } catch (error) {
        this.logger.error(`Middleware error: ${error.message}`);
      }
    }

    return processedEvent;
  }

  // Enhanced emit with middleware support
  async emitWithMiddleware(eventType: string, payload: any): Promise<boolean> {
    try {
      const eventPayload: EventPayload = {
        type: eventType,
        data: payload,
        timestamp: new Date().toISOString(),
        ...payload,
      };

      // Process through middlewares
      const processedEvent = await this.processMiddlewares(eventPayload);
      if (processedEvent === null) {
        this.logger.debug(`Event cancelled by middleware: ${eventType}`);
        return false;
      }

      // Emit the processed event
      return this.emit(eventType, processedEvent);
    } catch (error) {
      this.logger.error(`Failed to emit event with middleware ${eventType}: ${error.message}`);
      return false;
    }
  }

  // Event history and replay
  async getEventHistory(eventType: string, limit: number = 100): Promise<EventPayload[]> {
    try {
      const key = `event_history:${eventType}`;
      const events = await this.redisService.get(key);
      return events ? events.slice(-limit) : [];
    } catch (error) {
      this.logger.error(`Failed to get event history for ${eventType}: ${error.message}`);
      return [];
    }
  }

  async storeEventHistory(eventType: string, payload: EventPayload): Promise<void> {
    try {
      const key = `event_history:${eventType}`;
      const events = await this.redisService.get(key) || [];
      events.push(payload);
      
      // Keep only last 1000 events
      if (events.length > 1000) {
        events.splice(0, events.length - 1000);
      }
      
      await this.redisService.set(key, events, 24 * 60 * 60); // 24 hours TTL
    } catch (error) {
      this.logger.error(`Failed to store event history: ${error.message}`);
    }
  }
}
